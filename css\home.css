

  #hero-canvas {
    width: 100%;
    height: 100%;
    display: block;
    background: red;
  }



.hero {
  position: relative;
  width: 100vw;
  height: 100svh;
     padding: var(--space-lg);

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  overflow-x: hidden;
  background: var(--secondary-color);
}

.hero h1 {
  font-size: clamp(2rem, 15vw, 15rem);
  line-height: 90%;
  overflow: hidden; /* Hide overflow for animation */
  will-change: transform, opacity; /* Optimize for animations */
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
  perspective: 1000px; /* Enable 3D transforms */
  z-index: 1;

}



/* home - hero img holder */
.hero-img-holder {
  position: relative;
  width: 100vw;
  height: 100svh;
    padding: var(--space-lg);

  background: var(--secondary-color);
}

.hero-img-holder .hero-img {
  position: relative;
  width: 100%;
  height: 100%;
  transform: translateY(-110%) scale(0.25) rotate(0);
  border: 0.3em solid var(--fg);
  overflow: hidden;
  
}


/* story  */

.story-section{
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    gap: 1rem;
}

.story-heading-wrapper{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  gap: 1rem;
}

.story-heading-gif{
    background: #000;
    width: clamp(4rem, 8vw, 8rem);
    height: clamp(2rem, 5vw, 5rem);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 0.2rem;
}

.story-content-wrapper{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
}

.story-text-wrapper{
  flex: 1;
}

.story-text-wrapper p{
  font-size: clamp(1.5rem,2.5vw,2.5rem);
    line-height: 120%;

    font-weight: 100;
}


.story-heading{
  font-size: clamp(2.5rem, 8vw, 8rem);
  line-height: 90%;
}


.story-text-wrapper p::selection,
.story-heading::selection{
  background: var(--primary-color);
  color: var(--secondary-color);
}

/* ABOUT  */
.about-section{
    width: 100%;
    height: auto;
    min-height: 100vh;
    background: var(--primary-color);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: 5rem;
    padding: var(--space-lg);
    color: var(--secondary-color);;
  }

  .about-bottom-wrapper{
    width: 100%;
    display: flex;
    align-items: end;
    justify-content: space-between;
   
    
}

  .about-top-wrapper h1{
     font-size: clamp(1.5rem,2.5vw,2.5rem);
    line-height: 120%;

    font-weight: 100;
}
  .about-bottom-text h2{
    font-size: clamp(2.5rem,3.5vw,3.5rem);
    line-height: 120%;
    font-weight: medium;
}

.about-top-wrapper h1::selection,
.about-bottom-text h2::selection{
  background: var(--secondary-color);
  color: var(--primary-color);
}

  .text-icon-wrapper{
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    overflow: hidden;
  }

#switch-lottie{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5;
}

/* WORK  */
.work-section{
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    color: var(--secondary-color);
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    gap: 1rem;
}

.work-heading-wrapper{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.work-heading-gif{
    background: #000;
    width: clamp(4rem, 8vw, 8rem);
    height: clamp(2rem, 5vw, 5rem);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 0.2rem;
}

.work-heading{
  font-size: clamp(2.5rem, 8vw, 8rem);
  line-height: 90%;
}


.work-heading::selection{
  background: var(--secondary-color);
  color: var(--primary-color);
}

.projects-wrapper{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 1rem;
}

.project-wrapper{
  width: 100%;
  height: 40rem;
  position: relative;
  border-radius: 2rem;
  overflow: hidden;
  margin-bottom: 2rem;
}

.project-img-wrapper{
  width: 100%;
  height: 100%;
  overflow: hidden;

}

.project-dets{
  background-color: var(--primary-color);
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.5rem;
  border-radius: 1000px;
  z-index: 10;
}


.projects-wrapper a{
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  z-index: 10;
  background: var(--primary-color);
  color: var(--secondary-color);
}

.projects-wrapper .btn-cta-border{
     color: transparent;

}

/* SCROLLER ANIMATION  */

.scroll-services{
  z-index: -2;
    position: relative;
    padding: 0 2rem;

}
  .centered-section {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5rem 2rem;
    z-index: -1;
    border-radius: 2rem;
    background: var(--secondary-color);
}

.fixed-svg {
    position: fixed;
    z-index: 100;
    top: 50%;
    left: 49.5%;
    transform: translate(-50%, -50%);
}

.text-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.text-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem; /* Default gap */
    font-size: clamp(1rem,2.5vw,2.5rem);
    color: var(--primary-color);
}


@media (max-width: 768px) {
    .story-content-wrapper{
        padding: 0;
    }

    .story-heading-wrapper{
  justify-content: space-between;
}
    .about-bottom-wrapper{
        flex-direction: column;
        align-items: start;
    }

    .project-wrapper{
  height: 20rem;
}






}